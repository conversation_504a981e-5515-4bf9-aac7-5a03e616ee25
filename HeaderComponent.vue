<template>
  <header id="header">
    <div class="logo">
      <span class="icon fa-gem"></span>
    </div>
    <div class="content">
      <div class="inner">
        <h1>{{ title }}</h1>
        <p>{{ description }}</p>
      </div>
    </div>
    <nav>
      <ul>
        <li v-for="item in navItems" :key="item.id">
          <a :href="item.href" @click.prevent="handleNavClick(item)">{{ item.text }}</a>
        </li>
      </ul>
    </nav>
  </header>
</template>

<script>
export default {
  name: 'HeaderComponent',
  props: {
    title: {
      type: String,
      default: 'Dimension'
    },
    description: {
      type: String,
      default: 'A fully responsive site template designed by HTML5 UP and released for free under the Creative Commons license.'
    },
    navItems: {
      type: Array,
      default: () => [
        { id: 1, text: 'Intro', href: '#intro' },
        { id: 2, text: 'Work', href: '#work' },
        { id: 3, text: 'About', href: '#about' },
        { id: 4, text: 'Contact', href: '#contact' }
      ]
    }
  },
  methods: {
    handleNavClick(item) {
      // Эмитируем событие для родительского компонента
      this.$emit('nav-click', item);
    }
  }
}
</script>

<style scoped>
/* Import FontAwesome */
@import url('assets/css/fontawesome-all.min.css');
@import url("https://fonts.googleapis.com/css?family=Source+Sans+Pro:300italic,600italic,300,600");

/* Reset */
* {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  box-sizing: border-box;
}

/* Typography */
a {
  -moz-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-bottom-color 0.2s ease-in-out;
  -webkit-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-bottom-color 0.2s ease-in-out;
  -ms-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-bottom-color 0.2s ease-in-out;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-bottom-color 0.2s ease-in-out;
  border-bottom: dotted 1px rgba(255, 255, 255, 0.5);
  text-decoration: none;
  color: inherit;
}

a:hover {
  border-bottom-color: transparent;
}

h1 {
  color: #ffffff;
  font-weight: 600;
  line-height: 1.3;
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5rem;
  font-size: 2.25rem;
}

p {
  margin: 0 0 2rem 0;
  color: #ffffff;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 300;
  font-size: 1rem;
  line-height: 1.65;
}

ul {
  list-style: none;
}

/* Icon */
.icon {
  text-decoration: none;
  border-bottom: none;
  position: relative;
}

.icon:before {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  text-transform: none !important;
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
  line-height: inherit;
}

.icon > .label {
  display: none;
}

.icon.solid:before {
  font-weight: 900;
}

/* Header */
#header {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.325s ease-in-out, filter 0.325s ease-in-out, opacity 0.325s ease-in-out;
  background: radial-gradient(rgba(0, 0, 0, 0.25) 25%, transparent 55%);
  max-width: 100%;
  text-align: center;
  font-family: "Source Sans Pro", sans-serif;
  color: #ffffff;
}

#header > * {
  transition: opacity 0.325s ease-in-out;
  position: relative;
  margin-top: 3.5rem;
}

#header > *:before {
  content: '';
  display: block;
  position: absolute;
  top: calc(-3.5rem - 1px);
  left: calc(50% - 1px);
  width: 1px;
  height: calc(3.5rem + 1px);
  background: #ffffff;
}

#header > :first-child {
  margin-top: 0;
}

#header > :first-child:before {
  display: none;
}

#header .logo {
  width: 5.5rem;
  height: 5.5rem;
  line-height: 5.5rem;
  border: solid 1px #ffffff;
  border-radius: 100%;
}

#header .logo .icon:before {
  font-size: 2rem;
}

#header .content {
  border-style: solid;
  border-color: #ffffff;
  border-top-width: 1px;
  border-bottom-width: 1px;
  max-width: 100%;
}

#header .content .inner {
  transition: max-height 0.75s ease, padding 0.75s ease, opacity 0.325s ease-in-out;
  transition-delay: 0.25s;
  padding: 3rem 2rem;
  max-height: 40rem;
  overflow: hidden;
}

#header .content .inner > :last-child {
  margin-bottom: 0;
}

#header .content p {
  text-transform: uppercase;
  letter-spacing: 0.2rem;
  font-size: 0.8rem;
  line-height: 2;
}

#header nav ul {
  display: flex;
  margin-bottom: 0;
  list-style: none;
  padding-left: 0;
  border: solid 1px #ffffff;
  border-radius: 4px;
}

#header nav ul li {
  padding-left: 0;
  border-left: solid 1px #ffffff;
}

#header nav ul li:first-child {
  border-left: 0;
}

#header nav ul li a {
  display: block;
  min-width: 7.5rem;
  height: 2.75rem;
  line-height: 2.75rem;
  padding: 0 1.25rem 0 1.45rem;
  text-transform: uppercase;
  letter-spacing: 0.2rem;
  font-size: 0.8rem;
  border-bottom: 0;
}

#header nav ul li a:hover {
  background-color: rgba(255, 255, 255, 0.075);
}

#header nav ul li a:active {
  background-color: rgba(255, 255, 255, 0.175);
}

/* Responsive styles */
@media screen and (max-width: 980px) {
  #header .content p br {
    display: none;
  }
}

@media screen and (max-width: 736px) {
  h1 {
    font-size: 1.75rem;
    line-height: 1.4;
  }

  #header > * {
    margin-top: 2rem;
  }

  #header > *:before {
    top: calc(-2rem - 1px);
    height: calc(2rem + 1px);
  }

  #header .logo {
    width: 4.75rem;
    height: 4.75rem;
    line-height: 4.75rem;
  }

  #header .logo .icon:before {
    font-size: 1.75rem;
  }

  #header .content .inner {
    padding: 2.5rem 1rem;
  }

  #header .content p {
    line-height: 1.875;
  }
}

@media screen and (max-width: 480px) {
  #header {
    padding: 1.5rem 0;
  }

  #header .content .inner {
    padding: 2.5rem 0;
  }

  #header nav ul {
    flex-direction: column;
    min-width: 10rem;
    max-width: 100%;
  }

  #header nav ul li {
    border-left: 0;
    border-top: solid 1px #ffffff;
  }

  #header nav ul li:first-child {
    border-top: 0;
  }

  #header nav ul li a {
    height: 3rem;
    line-height: 3rem;
    min-width: 0;
    width: 100%;
  }
}
</style>
