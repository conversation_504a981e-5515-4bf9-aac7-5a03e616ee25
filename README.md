# Header Component

Извлеченная шапка из шаблона Dimension by HTML5 UP для использования в качестве отдельного компонента.

## Файлы

### Основные файлы:
- `header-only.html` - Standalone HTML файл с шапкой
- `HeaderComponent.vue` - Vue.js компонент
- `assets/css/fontawesome-all.min.css` - Стили FontAwesome для иконок
- `assets/webfonts/` - Шрифты FontAwesome

## Использование

### HTML версия
Просто откройте `header-only.html` в браузере. Все стили встроены в файл.

### Vue компонент

```vue
<template>
  <div>
    <HeaderComponent 
      :title="'Мой сайт'"
      :description="'Описание моего сайта'"
      :nav-items="navigationItems"
      @nav-click="handleNavigation"
    />
  </div>
</template>

<script>
import HeaderComponent from './HeaderComponent.vue'

export default {
  components: {
    HeaderComponent
  },
  data() {
    return {
      navigationItems: [
        { id: 1, text: 'Главная', href: '#home' },
        { id: 2, text: 'О нас', href: '#about' },
        { id: 3, text: 'Услуги', href: '#services' },
        { id: 4, text: 'Контакты', href: '#contact' }
      ]
    }
  },
  methods: {
    handleNavigation(item) {
      console.log('Clicked:', item);
      // Ваша логика навигации
    }
  }
}
</script>
```

## Свойства Vue компонента

- `title` (String) - Заголовок в шапке (по умолчанию: "Dimension")
- `description` (String) - Описание под заголовком
- `navItems` (Array) - Массив элементов навигации с полями:
  - `id` - уникальный идентификатор
  - `text` - текст ссылки
  - `href` - адрес ссылки

## События

- `nav-click` - Эмитируется при клике на элемент навигации, передает объект элемента

## Особенности

- ✅ Полностью адаптивный дизайн
- ✅ Анимации при наведении
- ✅ Поддержка FontAwesome иконок
- ✅ Темная тема
- ✅ Кроссбраузерная совместимость

## Адаптивность

- **Desktop**: Горизонтальная навигация
- **Tablet** (≤736px): Уменьшенные размеры
- **Mobile** (≤480px): Вертикальная навигация

## Зависимости

- FontAwesome 5 (включен)
- Google Fonts: Source Sans Pro (загружается автоматически)

## Лицензия

Основано на шаблоне Dimension by HTML5 UP, распространяется под лицензией Creative Commons.
